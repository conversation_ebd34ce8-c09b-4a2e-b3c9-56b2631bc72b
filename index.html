<!DOCTYPE html>
<html lang="en">
<head>
  <div style="margin:2em 0 1em 0;text-align:center;">
    <label style="font-weight:600;">Upload HTML summary to fill the form:</label>
    <input type="file" id="html-upload" accept="text/html,.html" style="margin-left:1em;">
  </div>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>🤖 AI Use Case Pitch</title>
    <style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}
* {
    box-sizing: border-box;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}

html,
body {
    margin: 0;
    padding: 0;
}
@media only screen {
    body {
        margin: 2em auto;
        max-width: 900px;
        color: rgb(55, 53, 47);
    }
}

body {
    line-height: 1.5;
    /* Removed white-space: pre-wrap to fix excessive spacing */
}

a,
a.visited {
    color: inherit;
    text-decoration: underline;
}

.pdf-relative-link-path {
    font-size: 80%;
    color: #444;
}

h1,
h2,
h3 {
    letter-spacing: -0.01em;
    line-height: 1.2;
    font-weight: 600;
    margin-bottom: 0;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 0.75em;
}

h1 {
    font-size: 1.875rem;
    margin-top: 1.875rem;
}

h2 {
    font-size: 1.5rem;
    margin-top: 1.5rem;
}

h3 {
    font-size: 1.25rem;
    margin-top: 1.25rem;
}

.source {
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 1.5em;
    word-break: break-all;
}

/* Make callouts more compact and left-aligned */
.callout {
    border-radius: 3px;
    padding: 0.5em 1em;
    text-align: left;
    margin: 0;
}
figure.callout > div {
    width: auto !important;
    text-align: left !important;
    margin: 0 !important;
}

figure {
    margin: 1.25em 0;
    page-break-inside: avoid;
}

figcaption {
    opacity: 0.5;
    font-size: 85%;
    margin-top: 0.5em;
}

mark {
    background-color: transparent;
}

.indented {
    padding-left: 1.5em;
}

hr {
    background: transparent;
    display: block;
    width: 100%;
    height: 1px;
    visibility: visible;
    border: none;
    border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
    max-width: 100%;
}

@media only print {
    img {
        max-height: 100vh;
        object-fit: contain;
    }
}

@page {
    margin: 1in;
}

.collection-content {
    font-size: 0.875rem;
}

.column-list {
    display: flex;
    justify-content: space-between;
}

.column {
    padding: 0 1em;
}

.column:first-child {
    padding-left: 0;
}

.column:last-child {
    padding-right: 0;
}

.table_of_contents-item {
    display: block;
    font-size: 0.875rem;
    line-height: 1.3;
    padding: 0.125rem;
}

.table_of_contents-indent-1 {
    margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
    margin-left: 3rem;
}

.table_of_contents-indent-3 {
    margin-left: 4.5rem;
}

.table_of_contents-link {
    text-decoration: none;
    opacity: 0.7;
    border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
    border: 1px solid rgba(55, 53, 47, 0.09);
    border-collapse: collapse;
}

table {
    border-left: none;
    border-right: none;
}

th,
td {
    font-weight: normal;
    padding: 0.25em 0.5em;
    line-height: 1.5;
    min-height: 1.5em;
    text-align: left;
}

th {
    color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
    margin: 0;
    margin-block-start: 0.6em;
    margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
    margin-block-start: 0.6em;
}

ul > li {
    list-style: disc;
}

ul.to-do-list {
    padding-inline-start: 0;
}

ul.to-do-list > li {
    list-style: none;
}

.to-do-children-checked {
    text-decoration: line-through;
    opacity: 0.375;
}

ul.toggle > li {
    list-style: none;
}

ul {
    padding-inline-start: 1.7em;
}

ul > li {
    padding-left: 0.1em;
}

ol {
    padding-inline-start: 1.6em;
}

ol > li {
    padding-left: 0.2em;
}

.mono ol {
    padding-inline-start: 2em;
}

.mono ol > li {
    text-indent: -0.4em;
}

.toggle {
    padding-inline-start: 0em;
    list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
    padding-left: 1.7em;
}

.toggle > li > details > summary {
    margin-left: -1.1em;
}

.selected-value {
    display: inline-block;
    padding: 0 0.5em;
    background: rgba(206, 205, 202, 0.5);
    border-radius: 3px;
    margin-right: 0.5em;
    margin-top: 0.3em;
    margin-bottom: 0.3em;
    white-space: nowrap;
}

.collection-title {
    display: inline-block;
    margin-right: 1em;
}

.page-description {
    margin-bottom: 2em;
}

.simple-table {
    margin-top: 1em;
    font-size: 0.875rem;
    empty-cells: show;
}
.simple-table td {
    height: 29px;
    min-width: 120px;
}

.simple-table th {
    height: 29px;
    min-width: 120px;
}

.simple-table-header-color {
    background: rgb(247, 246, 243);
    color: black;
}
.simple-table-header {
    font-weight: 500;
}

time {
    opacity: 0.5;
}

.icon {
    display: inline-block;
    max-width: 1.2em;
    max-height: 1.2em;
    text-decoration: none;
    vertical-align: text-bottom;
    margin-right: 0.5em;
}

img.icon {
    border-radius: 3px;
}

.user-icon {
    width: 1.5em;
    height: 1.5em;
    border-radius: 100%;
    margin-right: 0.5rem;
}

.user-icon-inner {
    font-size: 0.8em;
}

.text-icon {
    border: 1px solid #000;
    text-align: center;
}

.page-cover-image {
    display: block;
    object-fit: cover;
    width: 100%;
    max-height: 30vh;
}

.page-header-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.page-header-icon-with-cover {
    margin-top: -0.72em;
    margin-left: 0.07em;
}

.page-header-icon img {
    border-radius: 3px;
}

.link-to-page {
    margin: 1em 0;
    padding: 0;
    border: none;
    font-weight: 500;
}

p > .user {
    opacity: 0.5;
}

td > .user,
td > time {
    white-space: nowrap;
}

input[type="checkbox"] {
    transform: scale(1.5);
    margin-right: 0.6em;
    vertical-align: middle;
}

p {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.image {
    border: none;
    margin: 1.5em 0;
    padding: 0;
    border-radius: 0;
    text-align: center;
}

.code,
code {
    background: rgba(135, 131, 120, 0.15);
    border-radius: 3px;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 85%;
    tab-size: 2;
}

code {
    color: #eb5757;
}

.code {
    padding: 1.5em 1em;
}

.code-wrap {
    white-space: pre-wrap;
    word-break: break-all;
}

.code > code {
    background: none;
    padding: 0;
    font-size: 100%;
    color: inherit;
}

blockquote {
    font-size: 1.25em;
    margin: 1em 0;
    padding-left: 1em;
    border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
    text-decoration: none;
    max-height: 8em;
    padding: 0;
    display: flex;
    width: 100%;
    align-items: stretch;
}

.bookmark-title {
    font-size: 0.85em;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 1.75em;
    white-space: nowrap;
}

.bookmark-text {
    display: flex;
    flex-direction: column;
}

.bookmark-info {
    flex: 4 1 180px;
    padding: 12px 14px 14px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.bookmark-image {
    width: 33%;
    flex: 1 1 180px;
    display: block;
    position: relative;
    object-fit: cover;
    border-radius: 1px;
}

.bookmark-description {
    color: rgba(55, 53, 47, 0.6);
    font-size: 0.75em;
    overflow: hidden;
    max-height: 4.5em;
    word-break: break-word;
}

.bookmark-href {
    font-size: 0.75em;
    margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
    color: rgba(50, 48, 44, 1);
}
.highlight-gray {
    color: rgba(115, 114, 110, 1);
    fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
    color: rgba(159, 107, 83, 1);
    fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
    color: rgba(217, 115, 13, 1);
    fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
    color: rgba(203, 145, 47, 1);
    fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
    color: rgba(68, 131, 97, 1);
    fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
    color: rgba(51, 126, 169, 1);
    fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
    color: rgba(144, 101, 176, 1);
    fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
    color: rgba(193, 76, 138, 1);
    fill: rgba(193, 76, 138, 1);
}
.highlight-red {
    color: rgba(205, 60, 58, 1);
    fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
    color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
    background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
    background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
    background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
    background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
    background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
    background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
    background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
    background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
    background: rgba(253, 235, 236, 1);
}
.block-color-default {
    color: inherit;
    fill: inherit;
}
.block-color-gray {
    color: rgba(115, 114, 110, 1);
    fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
    color: rgba(159, 107, 83, 1);
    fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
    color: rgba(217, 115, 13, 1);
    fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
    color: rgba(203, 145, 47, 1);
    fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
    color: rgba(68, 131, 97, 1);
    fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
    color: rgba(51, 126, 169, 1);
    fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
    color: rgba(144, 101, 176, 1);
    fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
    color: rgba(193, 76, 138, 1);
    fill: rgba(193, 76, 138, 1);
}
.block-color-red {
    color: rgba(205, 60, 58, 1);
    fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
    color: inherit;
    fill: inherit;
}
.block-color-gray_background {
    background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
    background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
    background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
    background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
    background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
    background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
    background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
    background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
    background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
    display: inline-flex;
    vertical-align: text-bottom;
    width: 16;
    height: 16;
    background-size: 16px;
    margin-left: 2px;
    margin-right: 5px;
}

.checkbox-on {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}

/* Improved style for text inputs and textareas */
input[type="text"],
input[type="date"],
select,
textarea {
    font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol" !important;
    font-size: 1em;
    border: 1px solid #bfc4c9;
    border-radius: 4px;
    padding: 0.5em 0.75em;
    background: #fafbfc;
    color: #222;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
    margin-top: 0.15em;
}
input[type="text"]:focus,
input[type="date"]:focus,
select:focus,
textarea:focus {
    border-color: #58a9d7;
    outline: none;
    background: #fff;
    box-shadow: 0 0 0 2px #e3f2fd;
}
textarea {
    resize: vertical;
    min-height: 2.5em;
    max-width: 100%;
}
    
</style>
</head>
<body class="sans">
    <form id="ai-use-case-form" autocomplete="off">
        <article id="22d3bdc6-e07c-8038-bcbb-fec85a478b2b" class="page">
            <header>
                <h1 style="display:flex; justify-content:center" class="page-title">🤖 AI Use Case Pitch</h1>
                <p class="page-description"></p>
            </header>
            <div class="page-body">
                <!-- Submitter Information -->
                <figure class="block-color-blue_background callout" style="display:flex" id="22d3bdc6-e07c-8092-8c34-ded80e897a99">
                    <div style="display:flex ;width:100%">
                        <label>👤 Submitter: 
                            <input type="text" name="submitter" style="width:100%">
                        </label>
                        <label>🏢 Department: 
                            <input type="text" name="department" style="width:100%">
                        </label>
                        <label>📅 Date Submitted: 
                            <input type="date" name="date_submitted" style="width:100%">
                        </label>
                    </div>
                </figure>

                <!-- Use Case Overview -->

                <details open="">
                    <summary style="font-weight:600;font-size:1.875em;line-height:1.3;margin:0">📋 Use Case Overview</summary>

                    <figure class="block-color-white_background callout" style="display:flex" id="22d3bdc6-e07c-80dd-9987-de1ee104b6ed">
                        <div style="display:flex; align-items:center; gap:1.25em; width:100%">
                            <strong style="min-width:10.5em;">✨ Use Case Name:</strong>
                            <input type="text" name="use_case_name" placeholder="Enter a clear, concise name for your AI use case" style="min-width:54ch;width:100%;max-width:100%;height:2.5em;">
                        </div>
                    </figure>

                    <!-- Description Section -->
                    <ul id="22d3bdc6-e07c-809a-9a2a-def449d060e1" class="block-color-default toggle">
                        <li>
                            <details open="">
                                <summary>🔍 <strong>Description</strong></summary>
                                <p id="22d3bdc6-e07c-8052-ab9d-c7aa9aec3f24" class="">
                                    <textarea placeholder="Provide a comprehensive description of your AI use case. What problem are you trying to solve? Which business processes would be affected?" name="description" rows="5" style="width:100%"></textarea>
                                </p>
                            </details>
                        </li>
                    </ul>

                    <!-- Current Pain Points -->
                    <ul id="22d3bdc6-e07c-80b8-816d-cddadaddc0d4" class="block-color-red toggle">
                        <li>
                            <details open="">
                                <summary>❗ <strong>Current Pain Points</strong></summary>
                                <p id="22d3bdc6-e07c-8031-b2aa-d9296d9733fc" class="">
                                    <textarea name="pain_points" placeholder="Describe the specific challenges or inefficiencies in the current process." style="width:100%"></textarea>
                                </p>
                            </details>
                        </li>
                    </ul>

                    <!-- Proposed AI Solution -->
                    <ul id="22d3bdc6-e07c-8007-b72c-c403195f847d" class="block-color-teal toggle">
                        <li>
                            <details open="">
                                <summary>💡<strong> Proposed AI Solution</strong></summary>
                                <p id="22d3bdc6-e07c-80d5-8cff-fb5b4bcc26e6" class="">
                                    <textarea name="solution" rows="4" placeholder="Outline how AI could address these pain points." style="width:100%"></textarea> 
                                </p>
                            </details>
                        </li>
                    </ul>

                </details>

                <!-- Business Impact Section -->
                <details open="">
                    <summary style="font-weight:600;font-size:1.875em;line-height:1.3;margin:0">💰 Business Impact</summary>
                    <div style="display: flex;" class="indented">
                        <table id="22d3bdc6-e07c-80a4-bd70-c0cd28a3a222" class="simple-table">
                            <tbody>
                                <tr>
                                    <td><mark class="highlight-default"><strong>📈 Impact Category</strong></mark></td>
                                    <td><mark class="highlight-default"><strong>Current Metrics</strong></mark></td>
                                    <td><mark class="highlight-default"><strong>Expected Improvement</strong></mark></td>
                                    <td><mark class="highlight-default"><strong>Notes</strong></mark></td>
                                </tr>
                                <tr>
                                    <td><mark class="highlight-default">⏱️ Time Saved (hours/week)</mark></td>
                                    <td><input type="text" name="impact_time_current" style="width:100%"></td>
                                    <td><input type="text" name="impact_time_expected" style="width:100%"></td>
                                    <td><input type="text" name="impact_time_notes" style="width:100%"></td>
                                </tr>
                                <tr>
                                    <td><mark class="highlight-default">💵 Cost Reduction (€/year)</mark></td>
                                    <td><input type="text" name="impact_cost_current" style="width:100%"></td>
                                    <td><input type="text" name="impact_cost_expected" style="width:100%"></td>
                                    <td><input type="text" name="impact_cost_notes" style="width:100%"></td>
                                </tr>
                                <tr>
                                    <td><mark class="highlight-default">📈 Revenue Increase (€/year)</mark></td>
                                    <td><input type="text" name="impact_revenue_current" style="width:100%"></td>
                                    <td><input type="text" name="impact_revenue_expected" style="width:100%"></td>
                                    <td><input type="text" name="impact_revenue_notes" style="width:100%"></td>
                                </tr>
                                <tr>
                                    <td><mark class="highlight-default">🎯 Quality Improvement (%)</mark></td>
                                    <td><input type="text" name="impact_quality_current" style="width:100%"></td>
                                    <td><input type="text" name="impact_quality_expected" style="width:100%"></td>
                                    <td><input type="text" name="impact_quality_notes" style="width:100%"></td>
                                </tr>
                                <tr>
                                    <td><mark class="highlight-default">😊 Customer Satisfaction</mark></td>
                                    <td><input type="text" name="impact_customer_current" style="width:100%"></td>
                                    <td><input type="text" name="impact_customer_expected" style="width:100%"></td>
                                    <td><input type="text" name="impact_customer_notes" style="width:100%"></td>
                                </tr>
                                <tr>
                                    <td><mark class="highlight-default">👨‍💼 Employee Satisfaction</mark></td>
                                    <td><input type="text" name="impact_employee_current" style="width:100%"></td>
                                    <td><input type="text" name="impact_employee_expected" style="width:100%"></td>
                                    <td><input type="text" name="impact_employee_notes" style="width:100%"></td>
                                </tr>
                            </tbody>
                        </table>

                        <!-- ROI Section -->
                        <figure class="block-color-white_background callout" style="display:flex" id="22d3bdc6-e07c-8033-a8d9-e0b1e3be9cb9">
                            <div style="width:100%">
                                <h3 id="22d3bdc6-e07c-80d7-b628-f01a1138b543" class="">💸 Return on Investment (ROI)</h3>
                                <p id="22d3bdc6-e07c-8008-8e8a-f9ac3dc2116d" class="">
                                    <strong>💰 Estimated Implementation Cost:</strong> €
                                    <input type="text" name="cost" style="width:100%">
                                </p>
                                <p id="22d3bdc6-e07c-80bb-851d-cc382bfe34e2" class="">
                                    <strong>💹 Estimated Annual Benefit:</strong> €
                                    <input type="text" name="benefit" style="width:100%">
                                </p>
                                <p id="22d3bdc6-e07c-8090-a263-f79cd0d89805" class="">
                                    <strong>⏳ Expected Payback Period:</strong> 
                                    <input type="text" name="payback" style="width:100%">
                                </p>
                                <p id="22d3bdc6-e07c-80f7-9ae1-dd50743f5aeb" class="">
                                    <strong>📊 3-Year ROI:</strong> 
                                    <input type="text" name="roi" style="width:100%">
                                </p>
                            </div>
                        </figure>
                    </div> 
                </details>

                <!-- Implementation Section -->
                <details open="">
                    <summary style="font-weight:600;font-size:1.875em;line-height:1.3;margin:0">⚙️ Implementation</summary>
                    <div class="indented">
                        <!-- Resources Required -->
                        <ul id="22d3bdc6-e07c-8014-9040-e6c2841478fb" class="block-color-orange toggle">
                            <li>
                                <details open="">
                                    <summary>🔧 <mark class="highlight-default"><strong>Resources Required</strong></mark></summary>
                                    <ul id="22d3bdc6-e07c-8047-b09b-cf6bbfd26bed" class="block-color-default_background bulleted-list">
                                        <li style="color:black">
                                            💻 <mark class="highlight-default_background">Technology:</mark>
                                            <textarea type="text" name="tech" placeholder="List required systems, tools, platforms" style="width:100%"></textarea>
                                        </li>
                                    </ul>
                                    <ul id="22d3bdc6-e07c-80d1-8511-d981f746fafb" class="bulleted-list">
                                        <li style="color:black">
                                            📊 Data: 
                                            <textarea type="text" name="data" placeholder="What data would be needed to implement this solution?" style="width:100%"></textarea>
                                        </li>
                                    </ul>
                                    <ul id="22d3bdc6-e07c-8002-8e57-ddececcca54a" class="block-color-default_background bulleted-list">
                                        <li style="list-style-type:disc">
                                            👥 <mark class="highlight-default_background">Team:</mark>
                                            <textarea type="text" name="team" placeholder="List required roles and expertise" style="width:100%"></textarea>
                                        </li>
                                    </ul>
                                    <ul id="22d3bdc6-e07c-8013-90fb-e637d34fbe5c" class="block-color-default_background bulleted-list">
                                        <li style="list-style-type:disc">
                                            🤝 <mark class="highlight-default_background">External Support:</mark>
                                            <textarea type="text" name="external_support" placeholder="List any external vendors or consultants needed" style="width:100%"></textarea>
                                        </li>
                                    </ul>
                                </details>
                            </li>
                        </ul>

                        <!-- Timeline -->
                        <ul id="22d3bdc6-e07c-8075-99de-ff92854ef420" class="block-color-brown toggle">
                            <li>
                                <details open="">
                                    <summary>📅 <mark class="highlight-default"><strong>Timeline</strong></mark></summary>
                                    <p id="22d3bdc6-e07c-80bd-ad58-d076fbf605e1" class="block-color-default_background">
                                        <mark class="highlight-default_background"><strong>⏱️ Estimated Implementation Time:</strong></mark>
                                        <strong> [weeks/months]</strong>
                                    </p>
                                    <table id="22d3bdc6-e07c-800c-acf5-f2dd5799a836" class="simple-table">
                                    <div style="display:flex; justify-content:center; width:100%;">
                                        <table id="22d3bdc6-e07c-800c-acf5-f2dd5799a836" class="simple-table" style="margin:auto;">
                                        <tbody>
                                            <tr>
                                                <td><mark class="highlight-default_background"><strong>Phase</strong></mark></td>
                                                <td><mark class="highlight-default_background"><strong>Duration</strong></mark></td>
                                                <td><mark class="highlight-default_background"><strong>Key Milestones</strong></mark></td>
                                            </tr>
                                            <tr>
                                                <td><mark class="highlight-default_background">🗓️ Planning</mark></td>
                                                <td><input type="text" name="phase_planning_duration" style="width:100%"></td>
                                                <td><input type="text" name="phase_planning_milestones" style="width:100%"></td>
                                            </tr>
                                            <tr>
                                                <td><mark class="highlight-default_background">🛠️ Development</mark></td>
                                                <td><input type="text" name="phase_development_duration" style="width:100%"></td>
                                                <td><input type="text" name="phase_development_milestones" style="width:100%"></td>
                                            </tr>
                                            <tr>
                                                <td><mark class="highlight-default_background">🧪 Testing</mark></td>
                                                <td><input type="text" name="phase_testing_duration" style="width:100%"></td>
                                                <td><input type="text" name="phase_testing_milestones" style="width:100%"></td>
                                            </tr>
                                            <tr>
                                                <td><mark class="highlight-default_background">🚀 Deployment</mark></td>
                                                <td><input type="text" name="phase_deployment_duration" style="width:100%"></td>
                                                <td><input type="text" name="phase_deployment_milestones" style="width:100%"></td>
                                            </tr>
                                        </tbody>
                                        </table>
                                    </div>
                                </details>
                            </li>
                        </ul>
                    </div>
                </details>

                <!-- Risks and Mitigation -->
                <details open="">
                    <summary style="font-weight:600;font-size:1.875em;line-height:1.3;margin:0">⚠️ Risks and Mitigation</summary>
                    <div class="indented">
                        <div style="display:flex; justify-content:center; width:100%;">
                        <table id="22d3bdc6-e07c-80a8-b67f-c0e14b4b203f" class="simple-table" style="margin:auto;">
                            <tbody>
                                <tr>
                                    <td><mark class="highlight-default"><strong>Risk Factor</strong></mark></td>
                                    <td><mark class="highlight-default"><strong>Impact</strong></mark></td>
                                    <td><mark class="highlight-default"><strong>Probability</strong></mark></td>
                                    <td><mark class="highlight-default"><strong>Mitigation Strategy</strong></mark></td>
                                </tr>
                                <tr>
                                    <td><input type="text" name="risk_factor_1" style="width:100%"></td>
                                    <td><input type="text" name="risk_impact_1" style="width:100%" placeholder="Low/Medium/High"></td>
                                    <td><input type="text" name="risk_probability_1" style="width:100%" placeholder="Low/Medium/High"></td>
                                    <td><input type="text" name="risk_mitigation_1" style="width:100%"></td>
                                </tr>
                                <tr>
                                    <td><input type="text" name="risk_factor_2" style="width:100%"></td>
                                    <td><input type="text" name="risk_impact_2" style="width:100%" placeholder="Low/Medium/High"></td>
                                    <td><input type="text" name="risk_probability_2" style="width:100%" placeholder="Low/Medium/High"></td>
                                    <td><input type="text" name="risk_mitigation_2" style="width:100%"></td>
                                </tr>
                                <tr>
                                    <td><input type="text" name="risk_factor_3" style="width:100%"></td>
                                    <td><input type="text" name="risk_impact_3" style="width:100%" placeholder="Low/Medium/High"></td>
                                    <td><input type="text" name="risk_probability_3" style="width:100%" placeholder="Low/Medium/High"></td>
                                    <td><input type="text" name="risk_mitigation_3" style="width:100%"></td>
                                </tr>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </details>

                <!-- Conclusion -->
                <details open="">
                    <summary style="font-weight:600;font-size:1.875em;line-height:1.3;margin:0">🏁 Conclusion</summary>
                    <div class="indented">
                        <!-- Approval Section -->
                        <figure class="block-color-white_background callout" id="22d3bdc6-e07c-80a0-993f-f5b778e5f44f">
                            <h3 id="22d3bdc6-e07c-800c-bacb-e65fee5b3299" class="">✅ Approval Section</h3>
                            <div class="approval-fields">
                                <label class="approval-label">
                                    Status:
                                    <select name="status" style="width:100%">
                                        <option>Approved</option>
                                        <option>Rejected</option>
                                        <option>Needs Revision</option>
                                    </select>
                                </label>
                                <label class="approval-label">
                                    Feedback:
                                    <textarea name="feedback" rows="3" style="width:100%"></textarea>
                                </label>
                                <label class="approval-label">
                                    Next Steps:
                                    <textarea name="next_steps" rows="3" style="width:100%"></textarea>
                                </label>
                            </div>
                        </figure>
                        <p id="22d3bdc6-e07c-80e6-bd81-d09e7334c903" class=""></p>
                    </div>
                </details>
            </div>
        </article>

        <div style="text-align: center; margin: 2em 0;">
            <input type="submit" value="Submit" id="submit-btn" style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 1em 2em;
                font-size: 1.1em;
                font-weight: 600;
                border-radius: 8px;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                transition: all 0.3s ease;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.6)'"
               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.4)'">
        </div>
    </form>
    <div id="presentation-output" style="display:none; margin-top:2em;"></div>
<script>
// Single script block for both HTML upload autofill and form submit summary presentation
document.addEventListener('DOMContentLoaded', function() {
  // HTML upload autofill logic
  var htmlUpload = document.getElementById('html-upload');
  if (htmlUpload) {
    htmlUpload.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (!file) return;
      const reader = new FileReader();
      reader.onload = function(ev) {
        const html = ev.target.result;
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const form = document.getElementById('ai-use-case-form');
        const fields = form.querySelectorAll('[name]');
        fields.forEach(field => {
          const name = field.name;
          const el = doc.querySelector(`[data-name="${name}"]`);
          if (el) {
            let value = el.innerHTML.replace(/<br\s*\/?>(\n)?/g, '\n').replace(/&nbsp;/g, ' ').replace(/\s+$/, '').replace(/^\s+/, '');
            value = value.replace(/<[^>]+>/g, '');
            if (field.tagName === 'INPUT' || field.tagName === 'TEXTAREA') {
              field.value = value;
            } else if (field.tagName === 'SELECT') {
              Array.from(field.options).forEach(opt => {
                if (opt.textContent.trim().toLowerCase() === value.trim().toLowerCase()) field.value = opt.value;
              });
            }
          }
        });
        alert('Form fields have been filled from the uploaded summary. Please review and adjust as needed.');
      };
      reader.readAsText(file);
    });
  }

  // Form submit summary presentation logic
  var form = document.getElementById('ai-use-case-form');
  if (form) {
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      // Show loading state
      const submitBtn = document.getElementById('submit-btn');
      const originalText = submitBtn.value;
      submitBtn.value = '⏳ Processing...';
      submitBtn.disabled = true;
      submitBtn.style.opacity = '0.7';

      // Small delay to show loading state
      setTimeout(function() {
        // Collect form data
        const data = Object.fromEntries(new FormData(form).entries());
      let html = `<div style='font-family:inherit;max-width:900px;margin:auto;background:#fff;border-radius:8px;box-shadow:0 2px 12px #0001;padding:2em;'>`;
      html += `<h1 style='font-size:2em;margin-bottom:0.5em;'>🤖 ${data.use_case_name || 'AI Use Case Pitch'}</h1>`;
      html += `<section style='margin-bottom:1.5em;'><h2 style='font-size:1.3em;color:#0ea5e9;'>👤 Submitter Information</h2><ul style='list-style:none;padding:0;'><li><strong>Submitter:</strong> <span data-name="submitter">${data.submitter||''}</span></li><li><strong>Department:</strong> <span data-name="department">${data.department||''}</span></li><li><strong>Date Submitted:</strong> <span data-name="date_submitted">${data.date_submitted||''}</span></li></ul></section>`;
      html += `<section style='margin-bottom:1.5em;'><h2 style='font-size:1.3em;color:#7c3aed;'>🔍 Description</h2><div><span data-name="description">${(data.description||'').replace(/\n/g,'<br>')}</span></div></section>`;
      html += `<section style='margin-bottom:1.5em;'><h2 style='font-size:1.3em;color:#e11d48;'>❗ Current Pain Points</h2><div><span data-name="pain_points">${(data.pain_points||'').replace(/\n/g,'<br>')}</span></div></section>`;
      html += `<section style='margin-bottom:1.5em;'><h2 style='font-size:1.3em;color:#059669;'>💡 Proposed AI Solution</h2><div><span data-name="solution">${(data.solution||'').replace(/\n/g,'<br>')}</span></div></section>`;
      html += `<section style='margin-bottom:1.5em;'><h2 style='font-size:1.3em;color:#b45309;'>📅 Timeline</h2>`;
      html += `<table style='width:100%;border-collapse:collapse;margin:1em 0;font-size:1em;'><thead><tr style='background:#f3f4f6;'><th>Phase</th><th>Duration</th><th>Key Milestones</th></tr></thead><tbody>`;
      html += `<tr><td>🗓️ Planning</td><td><span data-name="phase_planning_duration">${data.phase_planning_duration||''}</span></td><td><span data-name="phase_planning_milestones">${data.phase_planning_milestones||''}</span></td></tr>`;
      html += `<tr><td>🛠️ Development</td><td><span data-name="phase_development_duration">${data.phase_development_duration||''}</span></td><td><span data-name="phase_development_milestones">${data.phase_development_milestones||''}</span></td></tr>`;
      html += `<tr><td>🧪 Testing</td><td><span data-name="phase_testing_duration">${data.phase_testing_duration||''}</span></td><td><span data-name="phase_testing_milestones">${data.phase_testing_milestones||''}</span></td></tr>`;
      html += `<tr><td>🚀 Deployment</td><td><span data-name="phase_deployment_duration">${data.phase_deployment_duration||''}</span></td><td><span data-name="phase_deployment_milestones">${data.phase_deployment_milestones||''}</span></td></tr>`;
      html += `</tbody></table></section>`;
      html += `<section style='margin-bottom:1.5em;'><h2 style='font-size:1.3em;color:#f59e42;'>💰 Business Impact</h2>`;
      html += `<table style='width:100%;border-collapse:collapse;margin:1em 0;font-size:1em;'><thead><tr style='background:#f3f4f6;'><th>Category</th><th>Current</th><th>Expected</th><th>Notes</th></tr></thead><tbody>`;
      html += `<tr><td>⏱️ Time Saved</td><td><span data-name="impact_time_current">${data.impact_time_current||''}</span></td><td><span data-name="impact_time_expected">${data.impact_time_expected||''}</span></td><td><span data-name="impact_time_notes">${data.impact_time_notes||''}</span></td></tr>`;
      html += `<tr><td>💵 Cost Reduction</td><td><span data-name="impact_cost_current">${data.impact_cost_current||''}</span></td><td><span data-name="impact_cost_expected">${data.impact_cost_expected||''}</span></td><td><span data-name="impact_cost_notes">${data.impact_cost_notes||''}</span></td></tr>`;
      html += `<tr><td>📈 Revenue Increase</td><td><span data-name="impact_revenue_current">${data.impact_revenue_current||''}</span></td><td><span data-name="impact_revenue_expected">${data.impact_revenue_expected||''}</span></td><td><span data-name="impact_revenue_notes">${data.impact_revenue_notes||''}</span></td></tr>`;
      html += `<tr><td>🎯 Quality Improvement</td><td><span data-name="impact_quality_current">${data.impact_quality_current||''}</span></td><td><span data-name="impact_quality_expected">${data.impact_quality_expected||''}</span></td><td><span data-name="impact_quality_notes">${data.impact_quality_notes||''}</span></td></tr>`;
      html += `<tr><td>😊 Customer Satisfaction</td><td><span data-name="impact_customer_current">${data.impact_customer_current||''}</span></td><td><span data-name="impact_customer_expected">${data.impact_customer_expected||''}</span></td><td><span data-name="impact_customer_notes">${data.impact_customer_notes||''}</span></td></tr>`;
      html += `<tr><td>👨‍💼 Employee Satisfaction</td><td><span data-name="impact_employee_current">${data.impact_employee_current||''}</span></td><td><span data-name="impact_employee_expected">${data.impact_employee_expected||''}</span></td><td><span data-name="impact_employee_notes">${data.impact_employee_notes||''}</span></td></tr>`;
      html += `</tbody></table></section>`;
      html += `<section style='margin-bottom:1.5em;'><h2 style='font-size:1.3em;color:#6366f1;'>💸 ROI</h2>`;
      html += `<ul style='list-style:none;padding:0;'>`;
      html += `<li><strong>Estimated Implementation Cost:</strong> €<span data-name="cost">${data.cost||''}</span></li>`;
      html += `<li><strong>Estimated Annual Benefit:</strong> €<span data-name="benefit">${data.benefit||''}</span></li>`;
      html += `<li><strong>Expected Payback Period:</strong> <span data-name="payback">${data.payback||''}</span></li>`;
      html += `<li><strong>3-Year ROI:</strong> <span data-name="roi">${data.roi||''}</span></li>`;
      html += `</ul></section>`;
      html += `<section style='margin-bottom:1.5em;'><h2 style='font-size:1.3em;color:#f59e42;'>🔧 Resources Required</h2>`;
      html += `<ul style='list-style:none;padding:0;'>`;
      html += `<li><strong>Technology:</strong> <span data-name="tech">${(data.tech||'').replace(/\n/g,'<br>')}</span></li>`;
      html += `<li><strong>Data Requirements:</strong> <span data-name="data">${(data.data||'').replace(/\n/g,'<br>')}</span></li>`;
      html += `<li><strong>Team:</strong> <span data-name="team">${(data.team||'').replace(/\n/g,'<br>')}</span></li>`;
      html += `<li><strong>External Support:</strong> <span data-name="external_support">${(data.external_support||'').replace(/\n/g,'<br>')}</span></li>`;
      html += `</ul></section>`;
      html += `<section style='margin-bottom:1.5em;'><h2 style='font-size:1.3em;color:#f59e42;'>⚠️ Risks and Mitigation</h2>`;
      html += `<table style='width:100%;border-collapse:collapse;margin:1em 0;font-size:1em;'><thead><tr style='background:#f3f4f6;'><th>Risk Factor</th><th>Impact</th><th>Probability</th><th>Mitigation</th></tr></thead><tbody>`;
      html += `<tr><td><span data-name="risk_factor_1">${data.risk_factor_1||''}</span></td><td><span data-name="risk_impact_1">${data.risk_impact_1||''}</span></td><td><span data-name="risk_probability_1">${data.risk_probability_1||''}</span></td><td><span data-name="risk_mitigation_1">${data.risk_mitigation_1||''}</span></td></tr>`;
      html += `<tr><td><span data-name="risk_factor_2">${data.risk_factor_2||''}</span></td><td><span data-name="risk_impact_2">${data.risk_impact_2||''}</span></td><td><span data-name="risk_probability_2">${data.risk_probability_2||''}</span></td><td><span data-name="risk_mitigation_2">${data.risk_mitigation_2||''}</span></td></tr>`;
      html += `<tr><td><span data-name="risk_factor_3">${data.risk_factor_3||''}</span></td><td><span data-name="risk_impact_3">${data.risk_impact_3||''}</span></td><td><span data-name="risk_probability_3">${data.risk_probability_3||''}</span></td><td><span data-name="risk_mitigation_3">${data.risk_mitigation_3||''}</span></td></tr>`;
      html += `</tbody></table></section>`;
      html += `<section style='margin-bottom:1.5em;'><h2 style='font-size:1.3em;color:#10b981;'>✅ Approval</h2>`;
      html += `<ul style='list-style:none;padding:0;'>`;
      html += `<li><strong>Status:</strong> <span data-name="status">${data.status||''}</span></li>`;
      html += `<li><strong>Feedback:</strong> <span data-name="feedback">${(data.feedback||'').replace(/\n/g,'<br>')}</span></li>`;
      html += `<li><strong>Next Steps:</strong> <span data-name="next_steps">${(data.next_steps||'').replace(/\n/g,'<br>')}</span></li>`;
      html += `</ul></section>`;
      html += `<div style='text-align:right;margin-top:2em;display:flex;gap:1em;justify-content:flex-end;'>`;
      html += `<button id='download-summary' style='padding:0.5em 1.2em;font-size:1em;background:#6366f1;color:#fff;border:none;border-radius:4px;cursor:pointer;'>Download HTML</button>`;
      html += `<button id='download-pdf' style='padding:0.5em 1.2em;font-size:1em;background:#059669;color:#fff;border:none;border-radius:4px;cursor:pointer;'>Download PDF</button>`;
      html += `</div>`;
      html += `</div>`;
      document.getElementById('presentation-output').innerHTML = html;
      document.getElementById('presentation-output').style.display = '';
      form.style.display = 'none';
      // Download buttons logic
      setTimeout(function() {
        document.getElementById('download-summary').onclick = function() {
          const blob = new Blob([document.getElementById('presentation-output').innerHTML], {type: 'text/html'});
          const a = document.createElement('a');
          a.href = URL.createObjectURL(blob);
          a.download = (data.use_case_name ? data.use_case_name.replace(/[^a-z0-9]/gi,'_') : 'ai_use_case') + '_summary.html';
          document.body.appendChild(a);
          a.click();
          setTimeout(()=>{document.body.removeChild(a);}, 100);
        };
        document.getElementById('download-pdf').onclick = function() {
          // Use print to PDF for now (browser native)
          const win = window.open('', '_blank');
          win.document.write('<html><head><title>AI Use Case Summary</title><style>body{font-family:sans-serif;}table{border-collapse:collapse;width:100%;}th,td{border:1px solid #ccc;padding:0.5em;}section{margin-bottom:1.5em;}h1{font-size:2em;}h2{font-size:1.3em;}ul{list-style:none;padding:0;}button{display:none;}</style></head><body>' + document.getElementById('presentation-output').innerHTML + '</body></html>');
          win.document.close();
          win.focus();
          setTimeout(()=>win.print(), 500);
        };
      }, 100);

      // Reset button state
      submitBtn.value = originalText;
      submitBtn.disabled = false;
      submitBtn.style.opacity = '1';

      }, 500); // End of setTimeout for loading state
    });
  }
});
</script>
</body>
</html>